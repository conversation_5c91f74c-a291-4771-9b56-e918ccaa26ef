class Appraisal < ApplicationRecord
  include HasUuid

  belongs_to :dealership
  belongs_to :customer
  belongs_to :customer_vehicle, optional: true

  belongs_to :sales_person, class_name: "User", optional: true
  belongs_to :created_by, class_name: "User", optional: true
  belongs_to :updated_by, class_name: "User", optional: true

  enum :status, {
    incomplete: 0,
    complete: 1,
    awarded: 2,
    archived: 3,
    deleted: 4
  }, default: :incomplete

  validates :completed_percentage, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }, allow_nil: true
  validates :awarded_value, :price, :give_price, numericality: true, allow_nil: true
  validates :awarded_notes, length: { maximum: 1000 }, allow_blank: true
end
