# frozen_string_literal: true

class Api::V1::Dealerships::AppraisalsController < Api::V1::BaseController
  def create
    validate_create_params!

    @appraisal = dealership.appraisals.new(
      customer:,
      sales_person: current_user,
      created_by: current_user,
      updated_by: current_user,
      status: :incomplete
    )

    @appraisal.save!
    @status_code = 201
    @status_message = "Appraisal created successfully"
    render :show, status: :created
  end

  private

  def validate_create_params!
    raise Errors::InvalidInput, "customer_uuid is required" if permitted_params[:customer_uuid].blank?
  end

  def permitted_params
    params.permit(:customer_uuid)
  end
end
