json.extract! appraisal, :uuid, :status

json.customer do
  json.extract! appraisal.customer, :uuid, :first_name, :last_name, :email, :phone_number, :full_name
end

json.sales_person do
  json.extract! appraisal.sales_person, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

json.created_by do
  json.extract! appraisal.created_by, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

json.updated_by do
  json.extract! appraisal.updated_by, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

if appraisal.customer_vehicle
  json.vehicle do
    json.extract! appraisal.customer_vehicle, :uuid, :make, :model, :build_year, :vin, :rego, :color
    json.photo_urls appraisal.customer_vehicle.photos.map { |photo| photo.url } if appraisal.customer_vehicle.photos.attached?
    if appraisal.customer_vehicle.brand
      json.brand do
        json.extract! appraisal.customer_vehicle.brand, :uuid, :name, :logo_url
      end
    end
  end
end

json.created_at format_iso8601_with_offset(appraisal.created_at)
json.updated_at format_iso8601_with_offset(appraisal.updated_at)
