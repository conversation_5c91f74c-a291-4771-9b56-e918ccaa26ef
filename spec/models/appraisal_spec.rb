require 'rails_helper'

RSpec.describe Appraisal, type: :model do
  describe 'associations' do
    it { should belong_to(:dealership) }
    it { should belong_to(:customer) }
    it { should belong_to(:customer_vehicle).optional }
    it { should belong_to(:sales_person).class_name('User').optional }
    it { should belong_to(:created_by).class_name('User').optional }
    it { should belong_to(:updated_by).class_name('User').optional }
  end

  describe 'validations' do
    it { should validate_numericality_of(:completed_percentage).is_greater_than_or_equal_to(0).is_less_than_or_equal_to(100).allow_nil }
    it { should validate_numericality_of(:awarded_value).allow_nil }
    it { should validate_numericality_of(:price).allow_nil }
    it { should validate_numericality_of(:give_price).allow_nil }
    it { should validate_length_of(:awarded_notes).is_at_most(1000).allow_blank }
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(incomplete: 0, complete: 1, awarded: 2, archived: 3, deleted: 4).backed_by_column_of_type(:integer) }
  end

  describe 'uuid' do
    it 'has a uuid of length 36' do
      appraisal = FactoryBot.create(:appraisal)
      expect(appraisal.uuid.length).to eq(36)
    end
    it 'is unique' do
      a1 = FactoryBot.create(:appraisal)
      a2 = FactoryBot.create(:appraisal)
      expect(a1.uuid).not_to eq(a2.uuid)
    end
  end
end
