FactoryBot.define do
  factory :dealership do
    sequence(:name) { |n| "Dealership #{n}" }
    sequence(:email) { |n| "dealership#{n}@example.com" }
    sequence(:website) { |n| "https://dealership#{n}.example.com" }
    phone { "+***********" }
    address_line1 { "123 Main St" }
    state { "NSW" }
    postcode { "2000" }
    country { :au }
    status { :active }
    association :dealership_group
    association :brand

    trait :suspended do
      status { :suspended }
    end

    trait :deleted do
      status { :deleted }
    end

    trait :without_abn do
      abn { nil }
    end

    trait :without_external_id do
      external_id { nil }
    end

    trait :without_logo do
      logo { nil }
    end

    trait :with_logo do
      after(:build) do |dealership|
        dealership.logo.attach(
          io: Rails.root.join("spec/fixtures/files/test_logo.png").open,
          filename: "test_logo.png",
          content_type: "image/png"
        )
      end
    end

    trait :with_invalid_logo_type do
      after(:build) do |dealership|
        dealership.logo.attach(
          io: StringIO.new("some text content"),
          filename: "test.txt",
          content_type: "text/plain"
        )
      end
    end

    trait :with_large_logo do
      after(:build) do |dealership|
        # Create a 6MB string of random data
        large_content = SecureRandom.bytes(6.megabytes)
        dealership.logo.attach(
          io: StringIO.new(large_content),
          filename: "large_logo.png",
          content_type: "image/png"
        )
      end
    end

    trait :without_dealership_group do
      dealership_group { nil }
    end

    trait :without_brand do
      brand { nil }
    end

    trait :with_xero_id do
      xero_id { "1234567890" }
    end

    trait :with_long_name do
      long_name { "Test Dealership Long Name" }
    end

    trait :with_invalid_email do
      email { "invalid-email" }
    end

    trait :au_based do
      country { :au }
      state { "Melbourne" }
      postcode { "10101" }
      phone { "+***********" }
      setting_time_zone { :melbourne }
    end

    trait :us_based do
      country { :us }
      state { "California" }
      postcode { "90210" }
      phone { "+12125551234" }
      setting_date_format { :mm_dd_yyyy }
      setting_time_zone { :utc }
      setting_distance_unit { :miles }
    end
  end
end
